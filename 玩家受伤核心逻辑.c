__int64 __fastcall PlayerController__MIDGHMNDKGI(__int64 a1, int a2)
{
  __int64 instance; // x0
  int n159; // w20
  __int64 v6; // x8
  int n19; // w0
  __int64 n2; // x0
  float v9; // s0
  unsigned int n2_3; // w8
  unsigned int n2_4; // w9
  float v12; // s0
  int v13; // w8
  float v14; // s0
  bool v15; // zf
  float v16; // s0
  bool v17; // zf
  unsigned int n2_2; // w8
  unsigned int n2_1; // w9
  float v20; // s0
  int n301_1; // w0
  int v22; // w9
  int n301; // w8
  __int64 result; // x0

  if ( (byte_490E3EE & 1) == 0 )
  {
    sub_184E408(&GameController_TypeInfo);
    byte_490E3EE = 1;
  }
  instance = *(_QWORD *)(a1 + 344);
  if ( !instance )
    goto LABEL_55;
  n159 = AIPPAPNPBNB__ECILJOCIIFO(instance, 0LL);
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = GameController__get_instance(0LL);
  if ( !instance || (v6 = *(_QWORD *)(instance + 112)) == 0 )
LABEL_55:
    sub_184E634(instance);
  n19 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
          *(_QWORD *)(v6 + 72),
          *(_QWORD *)(v6 + 80),
          0LL);
  if ( n159 <= 159 && n19 >= 19 )
  {
    a2 += 40;
    goto LABEL_11;
  }
  if ( n159 < 173 )
  {
    if ( n159 >= 171 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483609;
      n2_4 = (int)v9 + 39;
      goto LABEL_45;
    }
    if ( n159 >= 169 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483611;
      n2_4 = (int)v9 + 37;
      goto LABEL_45;
    }
    if ( n159 >= 167 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483613;
      n2_4 = (int)v9 + 35;
      goto LABEL_45;
    }
    if ( n159 >= 165 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483614;
      n2_4 = (int)v9 + 34;
      goto LABEL_45;
    }
    if ( n159 >= 163 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483618;
      n2_4 = (int)v9 + 30;
      goto LABEL_45;
    }
    if ( n159 >= 161 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483619;
      n2_4 = (int)v9 + 29;
      goto LABEL_45;
    }
LABEL_11:
    if ( n159 >= 156 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483620;
      n2_4 = (int)v9 + 28;
    }
    else if ( n159 >= 151 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483621;
      n2_4 = (int)v9 + 27;
    }
    else
    {
      if ( n159 >= 101 )
      {
        v14 = (float)n159 * 0.25;
        n2_3 = -2147483629;
        n2_4 = (int)v14 + 19;
        v15 = v14 == INFINITY;
        goto LABEL_46;
      }
      if ( n159 < 51 )
      {
        if ( n159 >= 31 )
        {
          v16 = (float)n159 * 0.15;
          v17 = v16 == INFINITY;
          n2_2 = (int)v16 + 5;
          n2_1 = -2147483643;
        }
        else
        {
          if ( n159 < 21 )
          {
            if ( n159 < 11 )
              n2 = 4294967293LL;
            else
              n2 = 2LL;
            goto LABEL_49;
          }
          v20 = (float)n159 * 0.1;
          v17 = v20 == INFINITY;
          n2_2 = (int)v20 + 3;
          n2_1 = -2147483645;
        }
        if ( v17 )
          n2 = n2_1;
        else
          n2 = n2_2;
        goto LABEL_49;
      }
      v9 = (float)n159 * 0.16;
      n2_3 = -2147483639;
      n2_4 = (int)v9 + 9;
    }
LABEL_45:
    v15 = v9 == INFINITY;
LABEL_46:
    if ( v15 )
      n2 = n2_3;
    else
      n2 = n2_4;
    goto LABEL_49;
  }
  v12 = (float)n159 * 0.3;
  v13 = (int)v12;
  if ( v12 == INFINITY )
    v13 = 0x80000000;
  n2 = (unsigned int)(n159 + v13 - 131);
LABEL_49:
  n301_1 = UnityEngine_Random__Range_69543328(n2, (unsigned int)n159, 0LL);
  v22 = a2 - (n301_1 & ~(n301_1 >> 31));
  n301 = n301_1;
  if ( v22 <= 1 )
    result = 1LL;
  else
    result = (unsigned int)v22;
  if ( n159 < 0 || n301 >= 301 )
    return UnityEngine_Random__Range_69543328(1LL, 99LL, 0LL);
  return result;
}